<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Page Importer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        
        .neomorphism {
            background: #f1f5f9;
            border-radius: 16px;
            box-shadow: 
                8px 8px 16px rgba(148, 163, 184, 0.3),
                -8px -8px 16px rgba(255, 255, 255, 0.8);
        }
        
        .neomorphism-inset {
            background: #f1f5f9;
            border-radius: 12px;
            box-shadow: 
                inset 4px 4px 8px rgba(148, 163, 184, 0.3),
                inset -4px -4px 8px rgba(255, 255, 255, 0.8);
        }
        
        .neomorphism-button {
            background: #f1f5f9;
            border-radius: 12px;
            box-shadow: 
                4px 4px 8px rgba(148, 163, 184, 0.3),
                -4px -4px 8px rgba(255, 255, 255, 0.8);
            transition: all 0.2s ease;
        }
        
        .neomorphism-button:hover {
            box-shadow: 
                2px 2px 4px rgba(148, 163, 184, 0.3),
                -2px -2px 4px rgba(255, 255, 255, 0.8);
        }
        
        .input-neomorphism {
            background: #f1f5f9;
            border: none;
            border-radius: 12px;
            box-shadow: 
                inset 4px 4px 8px rgba(148, 163, 184, 0.3),
                inset -4px -4px 8px rgba(255, 255, 255, 0.8);
            padding: 12px 16px;
            outline: none;
            transition: all 0.2s ease;
        }
        
        .input-neomorphism:focus {
            box-shadow: 
                inset 2px 2px 4px rgba(148, 163, 184, 0.4),
                inset -2px -2px 4px rgba(255, 255, 255, 0.9),
                0 0 0 2px rgba(59, 130, 246, 0.3);
        }
        
        .facebook-blue {
            background: linear-gradient(135deg, #1877f2, #42a5f5);
        }
        
        .loading-spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #1877f2;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="min-h-screen p-6">
    
    <!-- Main Container -->
    <div class="max-w-4xl mx-auto">
        
        <!-- Header -->
        <div class="neomorphism p-8 mb-6">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-800 mb-4">Facebook Page Importer</h1>
                <p class="text-lg text-gray-600">Import data from an existing Facebook business page</p>
            </div>
        </div>

        <!-- Import Methods -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            
            <!-- Method 1: URL Import -->
            <div class="neomorphism p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">🔗 Import from URL</h2>
                <p class="text-gray-600 mb-4">Enter a Facebook page URL to automatically extract information</p>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Facebook Page URL</label>
                        <input type="url" id="facebookUrl" class="input-neomorphism w-full" 
                               placeholder="https://www.facebook.com/your-business-page">
                    </div>
                    
                    <button id="importFromUrl" class="neomorphism-button w-full py-3 text-gray-700 font-semibold">
                        <span id="urlImportText">🔍 Import from URL</span>
                        <div id="urlLoadingSpinner" class="loading-spinner mx-auto hidden"></div>
                    </button>
                </div>
                
                <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p class="text-xs text-yellow-800">
                        <strong>Note:</strong> Due to CORS restrictions, this demo uses simulated data. 
                        In production, this would use Facebook Graph API.
                    </p>
                </div>
            </div>

            <!-- Method 2: Manual Entry -->
            <div class="neomorphism p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">✍️ Manual Entry</h2>
                <p class="text-gray-600 mb-4">Manually enter information from your Facebook page</p>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Business Name</label>
                        <input type="text" id="manualBusinessName" class="input-neomorphism w-full" 
                               placeholder="Your Business Name">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Business Description</label>
                        <textarea id="manualDescription" class="input-neomorphism w-full h-20 resize-none" 
                                  placeholder="Brief description of your business"></textarea>
                    </div>
                    
                    <button id="useManualData" class="neomorphism-button w-full py-3 text-gray-700 font-semibold">
                        📝 Use Manual Data
                    </button>
                </div>
            </div>
        </div>

        <!-- Sample Facebook Pages -->
        <div class="neomorphism p-6 mb-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">📋 Try Sample Pages</h2>
            <p class="text-gray-600 mb-4">Test with pre-configured sample business data</p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button class="sample-page neomorphism-inset p-4 text-left hover:bg-blue-50 transition-colors" 
                        data-sample="fashion">
                    <div class="text-2xl mb-2">👕</div>
                    <div class="font-semibold text-gray-800">Fashion Store</div>
                    <div class="text-sm text-gray-600">Trendy Fashion Boutique</div>
                </button>
                
                <button class="sample-page neomorphism-inset p-4 text-left hover:bg-green-50 transition-colors" 
                        data-sample="restaurant">
                    <div class="text-2xl mb-2">🍕</div>
                    <div class="font-semibold text-gray-800">Restaurant</div>
                    <div class="text-sm text-gray-600">Mario's Italian Kitchen</div>
                </button>
                
                <button class="sample-page neomorphism-inset p-4 text-left hover:bg-purple-50 transition-colors" 
                        data-sample="tech">
                    <div class="text-2xl mb-2">💻</div>
                    <div class="font-semibold text-gray-800">Tech Services</div>
                    <div class="text-sm text-gray-600">Digital Solutions Pro</div>
                </button>
            </div>
        </div>

        <!-- Extracted Data Preview -->
        <div class="neomorphism p-6 mb-6" id="extractedDataSection" style="display: none;">
            <h2 class="text-xl font-bold text-gray-800 mb-4">📊 Extracted Data</h2>
            <div id="extractedDataContent" class="space-y-4">
                <!-- Data will be populated here -->
            </div>
            
            <div class="flex gap-4 mt-6">
                <button id="editData" class="neomorphism-button px-6 py-3 text-gray-700 font-semibold">
                    ✏️ Edit Data
                </button>
                <button id="generatePageFromData" class="facebook-blue px-6 py-3 text-white font-semibold rounded-lg">
                    🚀 Generate Page
                </button>
            </div>
        </div>

        <!-- Manual Edit Form -->
        <div class="neomorphism p-6" id="editDataSection" style="display: none;">
            <h2 class="text-xl font-bold text-gray-800 mb-4">✏️ Edit Extracted Data</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Business Name</label>
                        <input type="text" id="editBusinessName" class="input-neomorphism w-full">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Business Emoji</label>
                        <input type="text" id="editBusinessEmoji" class="input-neomorphism w-full" maxlength="2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tagline</label>
                        <input type="text" id="editTagline" class="input-neomorphism w-full">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                        <input type="text" id="editPhone" class="input-neomorphism w-full">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                        <input type="text" id="editAddress" class="input-neomorphism w-full">
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Hero Title</label>
                        <input type="text" id="editHeroTitle" class="input-neomorphism w-full">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Hero Subtitle</label>
                        <input type="text" id="editHeroSubtitle" class="input-neomorphism w-full">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">About Text</label>
                        <textarea id="editAboutText" class="input-neomorphism w-full h-20 resize-none"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Store Hours</label>
                        <input type="text" id="editHours" class="input-neomorphism w-full">
                    </div>
                </div>
            </div>
            
            <div class="flex gap-4 mt-6">
                <button id="saveEdits" class="facebook-blue px-6 py-3 text-white font-semibold rounded-lg">
                    💾 Save Changes
                </button>
                <button id="cancelEdits" class="neomorphism-button px-6 py-3 text-gray-700 font-semibold">
                    ❌ Cancel
                </button>
            </div>
        </div>
    </div>    <script>
        // Sample business data for different types
        const sampleBusinessData = {
            fashion: {
                businessName: "Chanta",
                businessEmoji: "👜",
                businessTagline: "Premium bags & accessories",
                businessPhone: "(*************",
                businessAddress: "123 Fashion Ave, Style City, SC 12345",
                businessHours: "Mon-Sat 10AM-8PM, Sun 12PM-6PM",
                heroTitle: "Get Inspired",
                heroSubtitle: "Browsing for your next long-haul trip, everyday journey, or just fancy a look at what's new? From community favourites to about-to-sell-out items, see them all here.",
                heroImage: "🎒",
                aboutText: "We are a premium bag and accessories brand dedicated to creating functional, stylish pieces for modern life. Our collection combines innovative design with sustainable materials.",
                yearsExperience: "8",
                happyCustomers: "25000",
                productsCount: "150",
                aboutImage: "🏪",
                products: [
                    { name: "Shibuya Totepack", description: "Recycled PET Rip Stop", price: "140.00", emoji: "🎒" },
                    { name: "SoFo Backpack City", description: "Recycled Coated Cotton Canvas", price: "280.00", emoji: "🎒" },
                    { name: "Gion Backpack Pro", description: "Waterproof Tarpaulin", price: "140.00", emoji: "🎒" },
                    { name: "SoFo Rolltop Backpack X", description: "Recycled Coated Cotton Canvas", price: "170.00", emoji: "🎒" },
                    { name: "Kiso Messenger Bag", description: "Organic Cotton Canvas", price: "95.00", emoji: "💼" },
                    { name: "Harajuku Daypack", description: "Recycled Polyester Ripstop", price: "120.00", emoji: "🎒" },
                    { name: "Ginza Travel Tote", description: "Premium Leather Alternative", price: "160.00", emoji: "👜" },
                    { name: "Akihabara Tech Pack", description: "Water-Resistant Nylon", price: "190.00", emoji: "💻" }
                ]
            },
            restaurant: {
                businessName: "Mario's Italian Kitchen",
                businessEmoji: "🍕",
                businessTagline: "Authentic Italian cuisine",
                businessPhone: "(*************",
                businessAddress: "456 Little Italy St, Food City, FC 67890",
                businessHours: "Tue-Sun 11AM-10PM, Closed Mondays",
                heroTitle: "Authentic Italian Experience",
                heroSubtitle: "Fresh ingredients, traditional recipes, modern presentation",
                heroImage: "🍝",
                aboutText: "Mario's Italian Kitchen has been serving authentic Italian cuisine for over 25 years. Our family recipes passed down through generations bring the true taste of Italy to your table.",
                yearsExperience: "25",
                happyCustomers: "50000",
                productsCount: "150",
                aboutImage: "👨‍🍳",
                products: [
                    { name: "Margherita Pizza", description: "Classic pizza with fresh mozzarella and basil", price: "18.99" },
                    { name: "Pasta Carbonara", description: "Creamy pasta with pancetta and parmesan", price: "16.99" },
                    { name: "Tiramisu", description: "Traditional Italian dessert with coffee and mascarpone", price: "8.99" }
                ]
            },
            tech: {
                businessName: "Digital Solutions Pro",
                businessEmoji: "💻",
                businessTagline: "Technology solutions for modern business",
                businessPhone: "(*************",
                businessAddress: "789 Tech Park Blvd, Innovation City, IC 13579",
                businessHours: "Mon-Fri 9AM-6PM, Weekends by appointment",
                heroTitle: "Digital Transformation Made Easy",
                heroSubtitle: "Custom software solutions for your business needs",
                heroImage: "🚀",
                aboutText: "Digital Solutions Pro specializes in creating custom software solutions that help businesses streamline operations and increase efficiency. Our team of experts delivers cutting-edge technology solutions.",
                yearsExperience: "8",
                happyCustomers: "500",
                productsCount: "50",
                aboutImage: "⚙️",
                products: [
                    { name: "Website Development", description: "Custom responsive websites built for your business", price: "2999.99" },
                    { name: "Mobile App", description: "Native mobile applications for iOS and Android", price: "4999.99" },
                    { name: "Cloud Migration", description: "Seamless migration to cloud infrastructure", price: "1999.99" }
                ]
            }
        };

        let currentData = {};

        // Function to simulate Facebook page data extraction
        function simulateFacebookExtraction(url) {
            return new Promise((resolve) => {
                setTimeout(() => {
                    // Simulate API response based on URL patterns
                    let data;
                    
                    if (url.includes('fashion') || url.includes('clothing') || url.includes('boutique')) {
                        data = sampleBusinessData.fashion;
                    } else if (url.includes('restaurant') || url.includes('food') || url.includes('kitchen')) {
                        data = sampleBusinessData.restaurant;
                    } else if (url.includes('tech') || url.includes('digital') || url.includes('software')) {
                        data = sampleBusinessData.tech;
                    } else {
                        // Default to fashion store for unknown URLs
                        data = {
                            ...sampleBusinessData.fashion,
                            businessName: "Sample Business",
                            businessTagline: "Extracted from Facebook page",
                            aboutText: "This business information was automatically extracted from the provided Facebook page URL."
                        };
                    }
                    
                    resolve(data);
                }, 2000); // Simulate 2-second API call
            });
        }

        // Function to display extracted data
        function displayExtractedData(data) {
            currentData = data;
            
            const content = document.getElementById('extractedDataContent');
            content.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <div class="neomorphism-inset p-3">
                            <span class="font-semibold text-gray-700">Business:</span>
                            <span class="ml-2">${data.businessEmoji} ${data.businessName}</span>
                        </div>
                        <div class="neomorphism-inset p-3">
                            <span class="font-semibold text-gray-700">Tagline:</span>
                            <span class="ml-2">${data.businessTagline}</span>
                        </div>
                        <div class="neomorphism-inset p-3">
                            <span class="font-semibold text-gray-700">Phone:</span>
                            <span class="ml-2">${data.businessPhone}</span>
                        </div>
                        <div class="neomorphism-inset p-3">
                            <span class="font-semibold text-gray-700">Address:</span>
                            <span class="ml-2">${data.businessAddress}</span>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="neomorphism-inset p-3">
                            <span class="font-semibold text-gray-700">Hero Title:</span>
                            <span class="ml-2">${data.heroTitle}</span>
                        </div>
                        <div class="neomorphism-inset p-3">
                            <span class="font-semibold text-gray-700">Experience:</span>
                            <span class="ml-2">${data.yearsExperience} years</span>
                        </div>
                        <div class="neomorphism-inset p-3">
                            <span class="font-semibold text-gray-700">Customers:</span>
                            <span class="ml-2">${data.happyCustomers}+ happy customers</span>
                        </div>
                        <div class="neomorphism-inset p-3">
                            <span class="font-semibold text-gray-700">Hours:</span>
                            <span class="ml-2">${data.businessHours}</span>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6">
                    <div class="neomorphism-inset p-4">
                        <span class="font-semibold text-gray-700">About:</span>
                        <p class="mt-2 text-gray-600">${data.aboutText}</p>
                    </div>
                </div>
                
                <div class="mt-6">
                    <h3 class="font-semibold text-gray-700 mb-3">Sample Products/Services:</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                        ${data.products.map(product => `
                            <div class="neomorphism-inset p-3">
                                <div class="font-semibold text-gray-800">${product.name}</div>
                                <div class="text-sm text-gray-600 mt-1">${product.description}</div>
                                <div class="text-lg font-bold text-blue-600 mt-2">$${product.price}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            
            document.getElementById('extractedDataSection').style.display = 'block';
        }

        // Function to populate edit form
        function populateEditForm(data) {
            document.getElementById('editBusinessName').value = data.businessName;
            document.getElementById('editBusinessEmoji').value = data.businessEmoji;
            document.getElementById('editTagline').value = data.businessTagline;
            document.getElementById('editPhone').value = data.businessPhone;
            document.getElementById('editAddress').value = data.businessAddress;
            document.getElementById('editHeroTitle').value = data.heroTitle;
            document.getElementById('editHeroSubtitle').value = data.heroSubtitle;
            document.getElementById('editAboutText').value = data.aboutText;
            document.getElementById('editHours').value = data.businessHours;
        }

        // Function to generate page with data
        function generatePageWithData(data) {
            // Create URL parameters for the page builder
            const params = new URLSearchParams({
                businessName: data.businessName,
                businessEmoji: data.businessEmoji,
                businessTagline: data.businessTagline,
                businessPhone: data.businessPhone,
                businessAddress: data.businessAddress,
                businessHours: data.businessHours,
                heroTitle: data.heroTitle,
                heroSubtitle: data.heroSubtitle,
                heroImage: data.heroImage,
                aboutText: data.aboutText,
                yearsExperience: data.yearsExperience,
                happyCustomers: data.happyCustomers,
                productsCount: data.productsCount,
                aboutImage: data.aboutImage,
                product1Name: data.products[0].name,
                product1Description: data.products[0].description,
                product1Price: data.products[0].price,
                product2Name: data.products[1].name,
                product2Description: data.products[1].description,
                product2Price: data.products[1].price,
                product3Name: data.products[2].name,
                product3Description: data.products[2].description,
                product3Price: data.products[2].price
            });
            
            // Open the page builder with pre-filled data
            window.open(`facebook-page-builder-tool.html?${params.toString()}`, '_blank');
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            
            // Import from URL
            document.getElementById('importFromUrl').addEventListener('click', async function() {
                const url = document.getElementById('facebookUrl').value.trim();
                
                if (!url) {
                    alert('Please enter a Facebook page URL');
                    return;
                }
                
                // Show loading state
                document.getElementById('urlImportText').style.display = 'none';
                document.getElementById('urlLoadingSpinner').classList.remove('hidden');
                this.disabled = true;
                
                try {
                    const data = await simulateFacebookExtraction(url);
                    displayExtractedData(data);
                } catch (error) {
                    alert('Error extracting data from Facebook page. Please try again.');
                } finally {
                    // Reset loading state
                    document.getElementById('urlImportText').style.display = 'inline';
                    document.getElementById('urlLoadingSpinner').classList.add('hidden');
                    this.disabled = false;
                }
            });
            
            // Manual data entry
            document.getElementById('useManualData').addEventListener('click', function() {
                const businessName = document.getElementById('manualBusinessName').value.trim();
                const description = document.getElementById('manualDescription').value.trim();
                
                if (!businessName) {
                    alert('Please enter a business name');
                    return;
                }
                
                const data = {
                    ...sampleBusinessData.fashion,
                    businessName: businessName,
                    aboutText: description || "This business information was manually entered.",
                    businessTagline: "Manually entered business"
                };
                
                displayExtractedData(data);
            });
            
            // Sample page buttons
            document.querySelectorAll('.sample-page').forEach(button => {
                button.addEventListener('click', function() {
                    const sampleType = this.dataset.sample;
                    const data = sampleBusinessData[sampleType];
                    displayExtractedData(data);
                });
            });
            
            // Edit data button
            document.getElementById('editData').addEventListener('click', function() {
                populateEditForm(currentData);
                document.getElementById('editDataSection').style.display = 'block';
                this.scrollIntoView({ behavior: 'smooth' });
            });
            
            // Save edits
            document.getElementById('saveEdits').addEventListener('click', function() {
                currentData = {
                    ...currentData,
                    businessName: document.getElementById('editBusinessName').value,
                    businessEmoji: document.getElementById('editBusinessEmoji').value,
                    businessTagline: document.getElementById('editTagline').value,
                    businessPhone: document.getElementById('editPhone').value,
                    businessAddress: document.getElementById('editAddress').value,
                    heroTitle: document.getElementById('editHeroTitle').value,
                    heroSubtitle: document.getElementById('editHeroSubtitle').value,
                    aboutText: document.getElementById('editAboutText').value,
                    businessHours: document.getElementById('editHours').value
                };
                
                displayExtractedData(currentData);
                document.getElementById('editDataSection').style.display = 'none';
                document.getElementById('extractedDataSection').scrollIntoView({ behavior: 'smooth' });
            });
            
            // Cancel edits
            document.getElementById('cancelEdits').addEventListener('click', function() {
                document.getElementById('editDataSection').style.display = 'none';
            });
            
            // Generate page from data
            document.getElementById('generatePageFromData').addEventListener('click', function() {
                generatePageWithData(currentData);
            });
        });
    </script>
</body>
</html>