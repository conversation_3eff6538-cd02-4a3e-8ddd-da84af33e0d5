<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Business Page Builder</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    spacing: {
                        '8': '8px',
                        '16': '16px',
                        '24': '24px',
                        '32': '32px',
                        '40': '40px',
                        '48': '48px',
                        '56': '56px',
                        '64': '64px',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        
        .neomorphism {
            background: #f1f5f9;
            border-radius: 16px;
            box-shadow: 
                8px 8px 16px rgba(148, 163, 184, 0.3),
                -8px -8px 16px rgba(255, 255, 255, 0.8),
                inset 2px 2px 4px rgba(148, 163, 184, 0.1);
        }
        
        .neomorphism-inset {
            background: #f1f5f9;
            border-radius: 12px;
            box-shadow: 
                inset 4px 4px 8px rgba(148, 163, 184, 0.3),
                inset -4px -4px 8px rgba(255, 255, 255, 0.8);
        }
        
        .neomorphism-button {
            background: #f1f5f9;
            border-radius: 12px;
            box-shadow: 
                4px 4px 8px rgba(148, 163, 184, 0.3),
                -4px -4px 8px rgba(255, 255, 255, 0.8);
            transition: all 0.2s ease;
        }
        
        .neomorphism-button:hover {
            box-shadow: 
                2px 2px 4px rgba(148, 163, 184, 0.3),
                -2px -2px 4px rgba(255, 255, 255, 0.8);
        }
        
        .neomorphism-button:active {
            box-shadow: 
                inset 2px 2px 4px rgba(148, 163, 184, 0.3),
                inset -2px -2px 4px rgba(255, 255, 255, 0.8);
        }
        
        .facebook-blue {
            background: #1877f2;
        }
        
        .grid-8pt {
            gap: 8px;
        }
        
        .grid-16pt {
            gap: 16px;
        }
        
        .grid-24pt {
            gap: 24px;
        }
    </style>
</head>
<body class="min-h-screen p-24">
    
    <!-- Main Container -->
    <div class="max-w-7xl mx-auto">
        
        <!-- Header -->
        <div class="neomorphism p-32 mb-24">
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-800 mb-16">Facebook Business Page Builder</h1>
                <p class="text-lg text-gray-600 mb-24">Create a professional shop page for your Facebook business in minutes</p>
                <button class="neomorphism-button px-32 py-16 text-white facebook-blue font-semibold">
                    🚀 Start Building Your Page
                </button>
            </div>
        </div>

        <!-- Feature Bento Layout -->
        <div class="grid grid-cols-12 grid-16pt">
            
            <!-- Large Feature Card - Page Templates -->
            <div class="col-span-8 neomorphism p-32">
                <div class="h-full flex flex-col">
                    <div class="flex items-center mb-24">
                        <div class="w-48 h-48 neomorphism-inset rounded-full flex items-center justify-center mr-16">
                            <span class="text-2xl">🎨</span>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-800 mb-8">Professional Templates</h2>
                            <p class="text-gray-600">Choose from 50+ professionally designed templates</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-3 grid-16pt flex-1">
                        <div class="neomorphism-inset p-16 text-center">
                            <div class="w-full h-32 bg-gradient-to-br from-blue-100 to-blue-200 rounded-8 mb-16"></div>
                            <span class="text-sm font-medium text-gray-700">Retail Store</span>
                        </div>
                        <div class="neomorphism-inset p-16 text-center">
                            <div class="w-full h-32 bg-gradient-to-br from-green-100 to-green-200 rounded-8 mb-16"></div>
                            <span class="text-sm font-medium text-gray-700">Restaurant</span>
                        </div>
                        <div class="neomorphism-inset p-16 text-center">
                            <div class="w-full h-32 bg-gradient-to-br from-purple-100 to-purple-200 rounded-8 mb-16"></div>
                            <span class="text-sm font-medium text-gray-700">Services</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Catalog -->
            <div class="col-span-4 neomorphism p-24">
                <div class="text-center">
                    <div class="w-56 h-56 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-24">
                        <span class="text-3xl">📦</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-16">Product Catalog</h3>
                    <p class="text-gray-600 mb-24">Add unlimited products with photos, descriptions, and pricing</p>
                    <button class="neomorphism-button px-24 py-12 text-gray-700 font-medium">
                        Add Products
                    </button>
                </div>
            </div>

            <!-- Payment Integration -->
            <div class="col-span-4 neomorphism p-24">
                <div class="text-center">
                    <div class="w-56 h-56 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-24">
                        <span class="text-3xl">💳</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-16">Payment Setup</h3>
                    <p class="text-gray-600 mb-24">Secure payment processing with multiple payment options</p>
                    <button class="neomorphism-button px-24 py-12 text-gray-700 font-medium">
                        Configure
                    </button>
                </div>
            </div>

            <!-- Analytics Dashboard -->
            <div class="col-span-4 neomorphism p-24">
                <div class="text-center">
                    <div class="w-56 h-56 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-24">
                        <span class="text-3xl">📊</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-16">Analytics</h3>
                    <p class="text-gray-600 mb-24">Track sales, visitors, and engagement metrics</p>
                    <button class="neomorphism-button px-24 py-12 text-gray-700 font-medium">
                        View Stats
                    </button>
                </div>
            </div>

            <!-- Social Media Integration -->
            <div class="col-span-4 neomorphism p-24">
                <div class="text-center">
                    <div class="w-56 h-56 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-24">
                        <span class="text-3xl">🔗</span>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-16">Social Integration</h3>
                    <p class="text-gray-600 mb-24">Connect with Instagram, WhatsApp, and other platforms</p>
                    <button class="neomorphism-button px-24 py-12 text-gray-700 font-medium">
                        Connect
                    </button>
                </div>
            </div>

        </div>

        <!-- Bottom CTA Section -->
        <div class="neomorphism p-32 mt-24 text-center">
            <h2 class="text-3xl font-bold text-gray-800 mb-16">Ready to Build Your Facebook Shop?</h2>
            <p class="text-lg text-gray-600 mb-32">Join thousands of businesses already selling on Facebook</p>
            <div class="flex justify-center grid-24pt">
                <button class="neomorphism-button px-32 py-16 text-white facebook-blue font-semibold">
                    Get Started Free
                </button>
                <button class="neomorphism-button px-32 py-16 text-gray-700 font-semibold">
                    Watch Demo
                </button>
            </div>
        </div>

    </div>

    <script>
        // Add interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers for buttons
            const buttons = document.querySelectorAll('.neomorphism-button');
            
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    // Add click animation
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                    
                    // Add functionality based on button text
                    const buttonText = this.textContent.trim();
                    console.log(`Clicked: ${buttonText}`);
                    
                    // You can add specific functionality here
                    if (buttonText.includes('Start Building')) {
                        alert('Welcome to Facebook Page Builder! Choose a template to get started.');
                    } else if (buttonText.includes('Get Started Free')) {
                        alert('Sign up process would start here!');
                    } else if (buttonText.includes('Watch Demo')) {
                        alert('Demo video would play here!');
                    }
                });
            });
        });
    </script>

</body>
</html>