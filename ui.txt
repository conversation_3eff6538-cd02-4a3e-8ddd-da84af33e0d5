<?xml version="1.0" encoding="UTF-8"?>
<udml version="1.0">
  <instructions>
    This UDML was generated from HTML element analysis on 7/2/2025.
    Element type: DIV
    Classes: relative, bg-white, dark:bg-neutral-900, border, border-transparent, dark:border-neutral-800, rounded-2xl, shadow-xl, w-11/12, max-w-6xl, h-[85vh], flex, flex-col
    Purpose: Represents a content container component.
  </instructions>

  <meta>
    <author>HTML to UDML Chrome Extension</author>
    <created>2025-07-02T22:30:21.577Z</created>
    <source>DOM Analysis</source>
    <url>https://aurachat.io/create</url>
  </meta>

  <styles>
    <style name="columnStyle">
      <property name="display" value="flex" />
      <property name="position" value="relative" />
      <property name="flexDirection" value="column" />
      <property name="justifyContent" value="normal" />
      <property name="alignItems" value="normal" />
      <property name="fontSize" value="16px" />
      <property name="fontWeight" value="400" />
      <property name="color" value="rgb(23, 23, 23)" />
      <property name="backgroundColor" value="rgb(255, 255, 255)" />
      <property name="border" value="0.830737px solid rgba(0, 0, 0, 0)" />
      <property name="borderRadius" value="16px" />
      <property name="width" value="1152px" />
      <property name="height" value="684.943px" />
    </style>
  </styles>

  <components>
    <component name="ColumnComponent">
      <ai-hint>
        This component represents a div element containing "Prompt BuilderResetAdapt from TemplateAdapt from H...".
        Purpose: content container
      </ai-hint>
      <column class="relative bg-white dark:bg-neutral-900 border border-transparent dark:border-neutral-800 rounded-2xl shadow-xl w-11/12 max-w-6xl h-[85vh] flex flex-col" style="columnStyle">
        <row class="flex justify-between items-center border-b border-neutral-200 dark:border-neutral-800 py-2 px-4 bg-neutral-100 dark:bg-neutral-900 rounded-t-2xl">
          <row class="flex items-center space-x-3">
            <heading class="text-xs uppercase font-medium text-neutral-700 dark:text-neutral-300" level="2">
              <text>Prompt Builder</text>
            </heading>
            <button class="text-[10px] text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300 border border-neutral-200 dark:border-neutral-800 hover:border-neutral-300 dark:hover:border-neutral-700 rounded-md px-2 py-0.5 flex items-center" type="submit">
              <box class="lucide lucide-rotate-ccw mr-1 opacity-50" />
              <text>Reset</text>
            </button>
          </row>
          <button class="text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-300 border border-neutral-200 dark:border-neutral-800 hover:border-neutral-300 dark:hover:border-neutral-700 rounded-full p-1" type="submit">
            <box class="lucide lucide-x" />
          </button>
        </row>
        <row class="flex flex-col md:flex-row flex-1 h-full overflow-hidden">
          <box class="w-full md:w-1/2 md:border-r border-neutral-200 dark:border-neutral-800 overflow-y-auto">
            <box class="border-b border-neutral-200 dark:border-neutral-800 pb-2">
              <row class="flex gap-2 mb-2 mt-4">
                <box class="flex-1 ml-4 p-2 border rounded-lg font-mono cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.5),inset_0_-1px_0_rgba(0,0,0,0.02)] dark:shadow-[inset_0_-1px_0_rgba(0,0,0,0.2)] bg-neutral-100 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-200/50 dark:hover:bg-neutral-700/50">
                  <row class="flex justify-between items-center">
                    <row class="flex items-center">
                      <box class="lucide lucide-file-text hidden lg:block mr-2 text-neutral-500 dark:text-neutral-400" />
                      <text class="text-[10px] sm:text-[11px] lg:text-xs">
                        <text>Adapt from Template</text>
                      </text>
                    </row>
                    <box class="lucide lucide-chevron-down text-neutral-500 dark:text-neutral-400 transform transition-transform duration-200" />
                  </row>
                </box>
                <box class="flex-1 p-2 mr-4 border rounded-lg font-mono cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.5),inset_0_-1px_0_rgba(0,0,0,0.02)] dark:shadow-[inset_0_-1px_0_rgba(0,0,0,0.2)] bg-neutral-100 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-200/50 dark:hover:bg-neutral-700/50">
                  <row class="flex justify-between items-center">
                    <row class="flex items-center">
                      <box class="lucide lucide-code hidden lg:block mr-2 text-neutral-500 dark:text-neutral-400" />
                      <text class="text-[10px] sm:text-[11px] lg:text-xs">
                        <text>Adapt from HTML</text>
                      </text>
                    </row>
                    <box class="lucide lucide-chevron-down text-neutral-500 dark:text-neutral-400 transform transition-transform duration-200" />
                  </row>
                </box>
              </row>
            </box>
            <box class="pt-4 py-2 border-b border-neutral-200 dark:border-neutral-800">
              <column class="flex flex-col space-y-5">
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <row class="flex items-center cursor-pointer">
                      <box class="lucide lucide-chevron-down text-neutral-500 dark:text-neutral-400 transform transition-transform duration-200 mr-2 rotate-0" />
                      <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400">
                        <text>Layout Type</text>
                      </text>
                    </row>
                    <button class="button-outline" type="submit">
                      <box class="lucide lucide-x w-2.5 h-2.5" />
                    </button>
                    <box class="ml-auto mr-4">
                      <row class="flex items-center text-[10px] font-medium bg-neutral-100 dark:bg-neutral-900 px-[2px] py-[2px] rounded-lg border border-neutral-200 dark:border-neutral-800 hover:bg-neutral-200/50 dark:hover:bg-neutral-800/50 hover:border-neutral-300 dark:hover:border-neutral-700">
                        <button class="px-2 py-0.5 text-[10px] rounded-md button-primary text-white" type="submit">
                          <text>Web</text>
                        </button>
                        <button class="px-2 py-0.5 text-[10px] rounded-md text-neutral-700 dark:text-neutral-300" type="submit">
                          <text>Mobile</text>
                        </button>
                      </row>
                    </box>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/4 flex-1" />
                              <box class="rounded-sm h-2/4 flex-1" />
                              <box class="rounded-sm h-1/4 flex-1" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Hero</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-blue-500 bg-opacity-10 border border-blue-500 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/4 flex-1" />
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-blue-500">
                          <text>Features</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/5 flex-1" />
                              <box class="rounded-sm h-2/5 flex-1" />
                              <row class="flex-1 flex items-center justify-center gap-1">
                                <box class="rounded-full w-1.5 h-1.5" />
                                <box class="rounded-full w-1.5 h-1.5" />
                                <box class="rounded-full w-1.5 h-1.5" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Onboarding</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 w-1/4" />
                                <column class="flex-1 flex flex-col gap-0.5">
                                  <box class="rounded-sm h-1/4 flex-1" />
                                  <box class="rounded-sm flex-1" />
                                </column>
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Docs</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/5 flex-1" />
                              <box class="rounded-sm h-1/6 flex-1" />
                              <box class="rounded-sm h-1/4 flex-1" />
                              <box class="rounded-sm h-1/3 flex-1" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Updates</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/5 flex-1" />
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Portfolio</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/4 flex-1" />
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Pricing</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Gallery</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/5 flex-1" />
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 w-1/4" />
                                <box class="rounded-sm flex-1" />
                              </row>
                              <box class="rounded-sm h-1/5 flex-1" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Dashboard</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="w-full h-full flex items-center justify-center p-1">
                                <box class="rounded-sm h-3/4 w-3/4" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Login</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/5 flex-1" />
                              <box class="rounded-sm h-3/5 flex-1" />
                              <box class="rounded-sm h-1/5 flex-1" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Email</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/4 flex-1" />
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Testimonials</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="w-full h-full flex items-center justify-center p-1">
                                <column class="w-3/4 h-3/4 flex flex-col gap-0.5">
                                  <box class="rounded-sm h-1/3 flex-1" />
                                  <box class="rounded-sm h-2/3 flex-1" />
                                </column>
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Payment</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <column class="flex-1 flex flex-col justify-end">
                                <box class="rounded-sm h-1/3 flex-1" />
                              </column>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Footer</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/5 flex-1" />
                              <box class="rounded-sm h-1/4 flex-1" />
                              <box class="rounded-sm h-1/4 flex-1" />
                              <box class="rounded-sm h-1/4 flex-1" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>FAQ</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/5 flex-1" />
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Explore</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 w-1/3" />
                                <column class="flex-1 flex flex-col gap-0.5">
                                  <box class="rounded-sm h-1/4 flex-1" />
                                  <box class="rounded-sm h-2/5 flex-1" />
                                  <box class="rounded-sm h-2/5 flex-1" />
                                </column>
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Settings</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/3 flex-1" />
                              <box class="rounded-sm h-2/3 flex-1" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>About</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/5 flex-1" />
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 w-2/5" />
                                <box class="rounded-sm flex-1" />
                              </row>
                              <box class="rounded-sm h-1/5 flex-1" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Blog</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/5 flex-1" />
                              <row class="flex-1 relative flex items-center justify-center">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-full w-2.5 h-2.5 absolute" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Video</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/5 flex-1" />
                              <box class="rounded-sm h-3/5 flex-1" />
                              <box class="rounded-sm h-1/5 flex-1" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Landing Page</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Layout Configuration</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="w-full h-full flex items-center justify-center p-1">
                                <box class="rounded-sm h-4/5 w-4/5 !rounded" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Card</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/5 flex-1" />
                              <box class="rounded-sm h-1/5 flex-1" />
                              <box class="rounded-sm h-1/5 flex-1" />
                              <box class="rounded-sm h-1/5 flex-1" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>List</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>2-2 Square</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Table</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Sidebar Left</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Sidebar Right</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>1-1 Split</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/2 flex-1" />
                              <box class="rounded-sm h-1/2 flex-1" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>1-1 Vertical</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                              </row>
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>1/3 2/3 Bento</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                              </row>
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>2/3 1/3 Bento</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-2/5 flex-1" />
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>1×4 Bento</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 w-1/2" />
                                <column class="flex-1 flex flex-col gap-0.5">
                                  <box class="rounded-sm flex-1" />
                                  <box class="rounded-sm flex-1" />
                                </column>
                              </row>
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Feature Bento</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 !flex-none" />
                                <column class="flex-1 flex flex-col gap-0.5">
                                  <box class="rounded-sm flex-1" />
                                  <box class="rounded-sm flex-1" />
                                </column>
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Featured Right</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/2 flex-1" />
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Featured Top</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                              </row>
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>1/4 2/4 1/4 Bento</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                              </row>
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>2/4 1/4 1/4 Bento</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>2-1 Split</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1 !flex-none" />
                                <box class="rounded-sm flex-1 !flex-none" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>1-2 Split</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>1-1-1 Equal</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm h-1/3 flex-1" />
                              <box class="rounded-sm flex-1" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Header Focus</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                              <row class="flex flex-1 gap-0.5">
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                                <box class="rounded-sm flex-1" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>3-3 Grid</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <column class="w-full h-full flex flex-col items-center justify-center p-1 gap-0.5">
                                <box class="rounded-sm h-3/5 w-4/5" />
                                <row class="flex items-center gap-1">
                                  <box class="rounded-sm h-1 w-1 !rounded-full" />
                                  <box class="rounded-sm h-1.5 w-1.5 !rounded-full" />
                                  <box class="rounded-sm h-1 w-1 !rounded-full" />
                                </row>
                              </column>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Carousel</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="w-full h-full flex items-center justify-center p-1">
                                <box class="rounded-sm h-3/4 w-3/4 !rounded-md" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Modal</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[42px] h-[42px] mt-2 mb-1">
                          <row class="w-full h-full p-0.5 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="w-full h-full flex items-center justify-center p-1">
                                <box class="rounded-sm h-2/5 w-3/4 !rounded-md" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Alert</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Framing</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="rounded-sm flex-1" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Full Screen</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex-1 flex items-center justify-center p-1 rounded">
                                <box class="w-4/5 h-4/5 rounded shadow-md p-0.5">
                                  <box class="rounded-sm flex-1" />
                                </box>
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Card</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <box class="h-2/6 rounded-t-sm" />
                              <box class="rounded-sm h-4/6 flex-1 rounded-b-sm !rounded-t-none" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Browser</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="h-2/6 rounded-t-sm flex items-center px-1 gap-0.5">
                                <box class="rounded-full w-1 h-1" />
                                <box class="rounded-full w-1 h-1" />
                                <box class="rounded-full w-1 h-1" />
                              </row>
                              <box class="rounded-sm h-4/6 flex-1 rounded-b-sm !rounded-t-none" />
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Mac App</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full p-1 flex items-center justify-center">
                            <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                              <row class="flex-1 flex items-center justify-center p-1">
                                <box class="w-4/5 h-4/5 rounded-lg" />
                              </row>
                            </column>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Clay Web</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
              </column>
            </box>
            <box class="pt-4 py-2 border-b border-neutral-200 dark:border-neutral-800">
              <column class="flex flex-col space-y-5">
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <row class="flex items-center cursor-pointer">
                      <box class="lucide lucide-chevron-down text-neutral-500 dark:text-neutral-400 transform transition-transform duration-200 mr-2 rotate-0" />
                      <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400">
                        <text>Style</text>
                      </text>
                    </row>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                            <box class="rounded-sm h-4" />
                            <box class="rounded-sm h-2" />
                            <box class="rounded-sm h-2" />
                          </column>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Flat</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                            <box class="rounded-sm h-4" />
                            <box class="rounded-sm h-2" />
                            <box class="rounded-sm h-2" />
                          </column>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Outline</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                            <box class="rounded-none h-4" />
                            <box class="rounded-none h-2" />
                            <box class="rounded-none h-2" />
                          </column>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Minimalist</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                            <box class="rounded-sm h-4 backdrop-blur-sm" />
                            <box class="rounded-sm h-2 backdrop-blur-sm" />
                            <box class="rounded-sm h-2 backdrop-blur-sm" />
                          </column>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Glass</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                            <box class="rounded-lg h-4" />
                            <box class="rounded-lg h-2" />
                            <box class="rounded-lg h-2" />
                          </column>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>iOS</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <column class="w-full h-full flex flex-col gap-0.5 opacity-100 dark:opacity-30">
                            <box class="h-4">
                              <box class="rounded-sm h-full shadow" />
                            </box>
                            <box class="rounded-sm h-2" />
                            <box class="rounded-sm h-2" />
                          </column>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Material</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Theme</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="relative w-full h-full flex items-center justify-center">
                            <row class="w-full h-full rounded-sm flex items-center justify-center bg-white">
                              <box class="absolute w-full top-1/2 h-px bg-black/15" />
                              <box class="text-yellow-400">
                                <box class="w-4 h-4" />
                              </box>
                            </row>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Light Mode</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="relative w-full h-full flex items-center justify-center">
                            <row class="w-full h-full rounded-sm flex items-center justify-center bg-neutral-800">
                              <box class="absolute w-full top-1/2 h-px bg-white/30" />
                              <box class="text-white">
                                <box class="w-4 h-4" />
                              </box>
                            </row>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Dark Mode</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Accent Color</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Primary</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Blue</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Indigo</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Violet</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Purple</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Fuchsia</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Pink</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Rose</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Red</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Orange</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Amber</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Yellow</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Lime</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Green</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Emerald</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Teal</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Cyan</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="w-5 h-5 rounded-full" />
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Sky</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Background Color</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Transparent</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Neutral</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Gray</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Slate</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Zinc</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Stone</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Blue</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Indigo</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Violet</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Purple</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Fuchsia</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Pink</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Rose</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Red</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Orange</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Amber</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Yellow</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Lime</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Green</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Emerald</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Teal</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Cyan</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 border border-neutral-200 dark:border-neutral-700" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Sky</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Border Color</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Transparent</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Neutral</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Gray</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Slate</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Zinc</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Stone</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Blue</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Indigo</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Violet</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Purple</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Fuchsia</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Pink</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Rose</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Red</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Orange</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Amber</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Yellow</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Lime</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Green</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Emerald</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Teal</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Cyan</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="rounded-md w-12 h-7 bg-white dark:bg-neutral-800" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Sky</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Shadow</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>None</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Small</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Medium</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow-md" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Large</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow-lg" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Extra Large</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow-xl" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>XXL</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Beautiful sm</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Beautiful md</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Beautiful lg</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Light Blue sm</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Light Blue md</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Light Blue lg</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow-[rgba(50,_50,_93,_0.25)_0px_50px_100px_-20px,_rgba(0,_0,_0,_0.3)_0px_30px_60px_-30px,_rgba(10,_37,_64,_0.35)_0px_-2px_6px_0px_inset]" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Bevel</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow-[rgba(0,_0,_0,_0.17)_0px_-23px_25px_0px_inset,_rgba(0,_0,_0,_0.15)_0px_-36px_30px_0px_inset,_rgba(0,_0,_0,_0.1)_0px_-79px_40px_0px_inset,_rgba(0,_0,_0,_0.06)_0px_2px_1px,_rgba(0,_0,_0,_0.09)_0px_4px_2px,_rgba(0,_0,_0,_0.09)_0px_8px_4px,_rgba(0,_0,_0,_0.09)_0px_16px_8px,_rgba(0,_0,_0,_0.09)_0px_32px_16px]" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>3D</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center bg-neutral-100">
                            <box class="rounded-md w-11 h-6 bg-white shadow-[rgba(50,_50,_93,_0.25)_0px_30px_60px_-12px_inset,_rgba(0,_0,_0,_0.3)_0px_18px_36px_-18px_inset]" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Inner Shadow</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
              </column>
            </box>
            <box class="pt-4 py-2 border-b border-neutral-200 dark:border-neutral-800">
              <column class="flex flex-col space-y-5">
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <row class="flex items-center cursor-pointer">
                      <box class="lucide lucide-chevron-down text-neutral-500 dark:text-neutral-400 transform transition-transform duration-200 mr-2 rotate-0" />
                      <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400">
                        <text>Typeface Family</text>
                      </text>
                    </row>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-sans text-neutral-500 dark:text-neutral-400">
                            <text>Type</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Sans</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-serif text-neutral-500 dark:text-neutral-400">
                            <text>Type</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Serif</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-mono text-neutral-500 dark:text-neutral-400">
                            <text>Type</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Monospace</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="tracking-tighter text-neutral-500 dark:text-neutral-400">
                            <text>Type</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Condensed</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="tracking-wider text-neutral-500 dark:text-neutral-400">
                            <text>Type</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Expanded</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-medium text-neutral-500 dark:text-neutral-400">
                            <text>Type</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Rounded</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-neutral-500 dark:text-neutral-400">
                            <text>Type</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Handwritten</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Heading Font</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-base font-sans text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Inter</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-base font-sans text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Geist</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-base font-sans text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Manrope</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-base font-serif text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Playfair Display</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-base font-serif text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Instrument Serif</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-base font-serif text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Plex Serif</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-base font-medium text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Nunito</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-base font-medium text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Varela Round</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-base font-mono text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Geist Mono</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-base font-mono text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Space Mono</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-base font-mono text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Source Code Pro</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Body &amp; UI Font</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-sm font-sans text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Inter</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-sm font-sans text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Geist</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-sm font-sans text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Manrope</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-sm font-serif text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Playfair Display</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-sm font-serif text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Instrument Serif</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-sm font-serif text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Plex Serif</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Nunito</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Varela Round</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-sm font-mono text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Geist Mono</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-sm font-mono text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Space Mono</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-sm font-mono text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Source Code Pro</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Heading Size</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-bold text-xs text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>20-32px</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-bold text-sm text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>32-40px</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-bold text-base text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>48-64px</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-bold text-lg text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>64-80px</text>
                        </text>
                      </button>
                    </row>
                  </box>
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Subheading Size</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-bold text-xs text-neutral-500 dark:text-neutral-400">
                            <text>Subtitle</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>16-20px</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-bold text-sm text-neutral-500 dark:text-neutral-400">
                            <text>Subtitle</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>20-24px</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-bold text-base text-neutral-500 dark:text-neutral-400">
                            <text>Subtitle</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>24-28px</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-bold text-lg text-neutral-500 dark:text-neutral-400">
                            <text>Subtitle</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>28-32px</text>
                        </text>
                      </button>
                    </row>
                  </box>
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Body Text Size</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-bold text-xs text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>12-14px</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-bold text-sm text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>14-16px</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-bold text-base text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>16-18px</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="font-bold text-lg text-neutral-500 dark:text-neutral-400">
                            <text>Body</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>18-20px</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Heading Font Weight</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-lg font-thin text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Ultralight</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-lg font-light text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Light</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-lg font-normal text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Regular</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-lg font-medium text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Medium</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-lg font-semibold text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Semibold</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-lg font-bold text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Bold</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <text class="text-lg font-black text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Black</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Heading Letter Spacing</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <column class="relative w-[68px] h-10 mt-2 mb-1 flex flex-col items-center justify-center gap-1">
                          <text class="text-lg tracking-tighter text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                          <row class="flex space-x-1">
                            <box class="w-1.5 h-1.5 rounded-sm bg-neutral-500 bg-opacity-20 dark:bg-neutral-400 dark:bg-opacity-20" />
                            <box class="w-1.5 h-1.5 rounded-sm bg-neutral-500 bg-opacity-20 dark:bg-neutral-400 dark:bg-opacity-20" />
                          </row>
                        </column>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Tighter</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <column class="relative w-[68px] h-10 mt-2 mb-1 flex flex-col items-center justify-center gap-1">
                          <text class="text-lg tracking-tight text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                          <row class="flex space-x-1">
                            <box class="w-1.5 h-1.5 rounded-sm bg-neutral-500 bg-opacity-20 dark:bg-neutral-400 dark:bg-opacity-20" />
                            <box class="w-1.5 h-1.5 rounded-sm bg-neutral-500 bg-opacity-20 dark:bg-neutral-400 dark:bg-opacity-20" />
                          </row>
                        </column>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Tight</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <column class="relative w-[68px] h-10 mt-2 mb-1 flex flex-col items-center justify-center gap-1">
                          <text class="text-lg tracking-normal text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                          <row class="flex space-x-1">
                            <box class="w-1.5 h-1.5 rounded-sm bg-neutral-500 bg-opacity-20 dark:bg-neutral-400 dark:bg-opacity-20" />
                            <box class="w-1.5 h-1.5 rounded-sm bg-neutral-500 bg-opacity-20 dark:bg-neutral-400 dark:bg-opacity-20" />
                          </row>
                        </column>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Normal</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <column class="relative w-[68px] h-10 mt-2 mb-1 flex flex-col items-center justify-center gap-1">
                          <text class="text-lg tracking-wide text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                          <row class="flex space-x-1">
                            <box class="w-1.5 h-1.5 rounded-sm bg-neutral-500 bg-opacity-20 dark:bg-neutral-400 dark:bg-opacity-20" />
                            <box class="w-1.5 h-1.5 rounded-sm bg-neutral-500 bg-opacity-20 dark:bg-neutral-400 dark:bg-opacity-20" />
                          </row>
                        </column>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Wide</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <column class="relative w-[68px] h-10 mt-2 mb-1 flex flex-col items-center justify-center gap-1">
                          <text class="text-lg tracking-wider text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                          <row class="flex space-x-1">
                            <box class="w-1.5 h-1.5 rounded-sm bg-neutral-500 bg-opacity-20 dark:bg-neutral-400 dark:bg-opacity-20" />
                            <box class="w-1.5 h-1.5 rounded-sm bg-neutral-500 bg-opacity-20 dark:bg-neutral-400 dark:bg-opacity-20" />
                          </row>
                        </column>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Wider</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <column class="relative w-[68px] h-10 mt-2 mb-1 flex flex-col items-center justify-center gap-1">
                          <text class="text-lg tracking-widest text-neutral-500 dark:text-neutral-400">
                            <text>Title</text>
                          </text>
                          <row class="flex space-x-1">
                            <box class="w-1.5 h-1.5 rounded-sm bg-neutral-500 bg-opacity-20 dark:bg-neutral-400 dark:bg-opacity-20" />
                            <box class="w-1.5 h-1.5 rounded-sm bg-neutral-500 bg-opacity-20 dark:bg-neutral-400 dark:bg-opacity-20" />
                          </row>
                        </column>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Widest</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
              </column>
            </box>
            <box class="pt-4 pb-16">
              <column class="flex flex-col space-y-5">
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <row class="flex items-center cursor-pointer">
                      <box class="lucide lucide-chevron-down text-neutral-500 dark:text-neutral-400 transform transition-transform duration-200 mr-2 rotate-0" />
                      <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400">
                        <text>Animation Type</text>
                      </text>
                    </row>
                    <box class="ml-auto mr-4">
                      <button class="text-[10px] font-medium bg-neutral-100 dark:bg-neutral-900 px-2 py-1 rounded-lg border border-neutral-200 dark:border-neutral-800 hover:bg-neutral-200/50 dark:hover:bg-neutral-800/50 hover:border-neutral-300 dark:hover:border-neutral-700" type="submit">
                        <text>Select Multiple</text>
                      </button>
                    </box>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Fade</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Slide</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Scale</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Rotate</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Blur</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>3D</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Pulse</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Shake</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Bounce</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Morph</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Skew</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Color</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Hue</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Perspective</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center">
                            <box class="w-5 h-5 rounded-sm" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Clip</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Scene</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="text-neutral-500">
                            <box class="lucide lucide-grid3x3 w-5 h-5" />
                          </box>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>All at once</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="text-neutral-500">
                            <box class="lucide lucide-panels-top-left w-5 h-5" />
                          </box>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Sequence</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="text-neutral-500">
                            <box class="lucide lucide-type w-5 h-5" />
                          </box>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Word by word</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="text-neutral-500">
                            <box class="lucide lucide-type w-5 h-5" />
                          </box>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Letter by letter</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <box class="mx-4">
                  <row class="flex justify-between items-center mb-2">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400">
                      <text>Duration</text>
                    </text>
                    <text class="text-[11px] text-neutral-500 dark:text-neutral-400">
                      <text>0.8s</text>
                    </text>
                  </row>
                  <input class="w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer dark:bg-neutral-700" type="range" value="0.8" />
                </box>
                <box class="mx-4">
                  <row class="flex justify-between items-center mb-2">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400">
                      <text>Delay</text>
                    </text>
                    <text class="text-[11px] text-neutral-500 dark:text-neutral-400">
                      <text>0.0s</text>
                    </text>
                  </row>
                  <input class="w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer dark:bg-neutral-700" type="range" value="0" />
                </box>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Timing</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center relative">
                            <box class="w-full h-0.5 bg-gray-300 absolute" />
                            <box class="w-2 h-2 rounded-full absolute" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Linear</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center relative">
                            <box class="w-full h-0.5 bg-gray-300 absolute" />
                            <box class="w-2 h-2 rounded-full absolute" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Ease</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center relative">
                            <box class="w-full h-0.5 bg-gray-300 absolute" />
                            <box class="w-2 h-2 rounded-full absolute" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Ease In</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center relative">
                            <box class="w-full h-0.5 bg-gray-300 absolute" />
                            <box class="w-2 h-2 rounded-full absolute" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Ease Out</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center relative">
                            <box class="w-full h-0.5 bg-gray-300 absolute" />
                            <box class="w-2 h-2 rounded-full absolute" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Ease In Out</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center relative">
                            <box class="w-full h-0.5 bg-gray-300 absolute" />
                            <box class="w-2 h-2 rounded-full absolute" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Spring</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center relative">
                            <box class="w-full h-0.5 bg-gray-300 absolute" />
                            <box class="w-2 h-2 rounded-full absolute" />
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Bounce</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Iterations</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="text-neutral-500">
                            <text class="text-xl">
                              <text>1×</text>
                            </text>
                          </box>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Once</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="text-neutral-500">
                            <text class="text-xl">
                              <text>2×</text>
                            </text>
                          </box>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Twice</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="text-neutral-500">
                            <text class="text-xl">
                              <text>3×</text>
                            </text>
                          </box>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Thrice</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <row class="relative w-[68px] h-10 mt-2 mb-1 flex items-center justify-center">
                          <box class="text-neutral-500">
                            <text class="text-xl">
                              <text>∞</text>
                            </text>
                          </box>
                        </row>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Infinite</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
                <column class="flex flex-col space-y-2.5">
                  <row class="flex items-center">
                    <text class="text-[11px] uppercase text-neutral-500 dark:text-neutral-400 pl-4">
                      <text>Direction</text>
                    </text>
                  </row>
                  <box class="overflow-x-auto pb-2">
                    <row class="flex gap-3 px-4 py-1 min-w-max">
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center relative">
                            <box class="absolute">
                              <text>→</text>
                            </box>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Normal</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center relative">
                            <box class="absolute">
                              <text>→</text>
                            </box>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Reverse</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center relative">
                            <box class="absolute">
                              <text>→</text>
                            </box>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Alternate</text>
                        </text>
                      </button>
                      <button class="relative group flex flex-col items-center w-20 h-[75px] rounded-lg bg-neutral-400 bg-opacity-5 hover:bg-opacity-10 border border-neutral-400 border-opacity-10 transition-colors" type="submit">
                        <box class="relative w-[68px] h-10 mt-2 mb-1">
                          <row class="w-full h-full flex items-center justify-center relative">
                            <box class="absolute">
                              <text>→</text>
                            </box>
                          </row>
                        </box>
                        <text class="text-[10px] truncate w-[75px] text-center text-neutral-700 dark:text-neutral-300">
                          <text>Alternate Reverse</text>
                        </text>
                      </button>
                    </row>
                  </box>
                </column>
              </column>
            </box>
          </box>
          <column class="w-full md:w-1/2 flex flex-col h-[300px] md:h-full">
            <box class="hidden md:block h-[460px]">
              <box class="h-full overflow-auto relative">
                <box class="bg-white dark:bg-neutral-950/30 p-4 h-full overflow-auto">
                  <box class="relative layout-preview sample-section preview-section animation-section mb-4">
                    <box class="label bg-black/80 text-white text-[10px] uppercase font-medium tracking-wider px-3 py-1 rounded-xl border border-white/15 shadow-lg">
                      <text>Animation:</text>
                      <text>None Selected</text>
                    </box>
                    <row class="h-64 flex items-center justify-center text-gray-400 text-sm">
                      <text>Select animation types to preview</text>
                    </row>
                  </box>
                </box>
              </box>
            </box>
            <box class="flex-1 overflow-y-auto p-4 border-t border-neutral-200 dark:border-neutral-800">
              <row class="flex justify-between items-center mb-2">
                <text class="text-[11px] uppercase font-regular text-neutral-500">
                  <text>Generated Prompts</text>
                </text>
                <button class="button-primary flex items-center gap-1" type="submit">
                  <box class="lucide lucide-lock" />
                  <text>Add to Prompt</text>
                </button>
              </row>
              <box class="mb-2">
                <box class="p-2 border rounded-lg font-mono cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.5),inset_0_-1px_0_rgba(0,0,0,0.02)] dark:shadow-[inset_0_-1px_0_rgba(0,0,0,0.2)] bg-neutral-100 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-200/50 dark:hover:bg-neutral-700/50">
                  <row class="flex justify-between items-center">
                    <row class="flex items-center">
                      <row class="w-3 h-3 mr-2 rounded-full border flex items-center justify-center border-neutral-200 bg-neutral-100" />
                      <text class="text-xs">
                        <text>Add custom prompt</text>
                      </text>
                    </row>
                    <input id="custom-prompt-checkbox" class="hidden" type="checkbox" value="on" />
                    <button class="text-blue-500 hover:text-blue-700 text-xs font-medium" type="submit">
                      <text>Add</text>
                    </button>
                  </row>
                </box>
              </box>
              <box class="space-y-2">
                <box class="p-2 border rounded-lg font-mono cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.5),inset_0_-1px_0_rgba(0,0,0,0.02)] dark:shadow-[inset_0_-1px_0_rgba(0,0,0,0.2)] bg-blue-100 border-blue-300 dark:bg-blue-900 dark:border-blue-400 hover:bg-blue-400/30 dark:hover:bg-blue-700/70">
                  <row class="flex justify-between items-center">
                    <row class="flex items-center">
                      <row class="w-3 h-3 mr-2 rounded-full border flex items-center justify-center border-blue-400 bg-blue-300">
                        <box class="w-1.5 h-1.5 rounded-full bg-blue-200" />
                      </row>
                      <text class="text-xs overflow-hidden text-ellipsis max-w-[220px] md:max-w-[240px] lg:max-w-[400px]">
                        <text>Create a features layout</text>
                      </text>
                    </row>
                    <button class="text-blue-500 hover:text-blue-700 text-xs font-medium flex items-center gap-1 opacity-70 cursor-not-allowed" type="submit">
                      <box class="lucide lucide-lock" />
                      <text>Add</text>
                    </button>
                  </row>
                </box>
                <box class="p-2 border rounded-lg font-mono cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.5),inset_0_-1px_0_rgba(0,0,0,0.02)] dark:shadow-[inset_0_-1px_0_rgba(0,0,0,0.2)] bg-neutral-100 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-200/50 dark:hover:bg-neutral-700/50">
                  <row class="flex justify-between items-center">
                    <row class="flex items-center">
                      <row class="w-3 h-3 mr-2 rounded-full border flex items-center justify-center border-neutral-200 bg-neutral-100" />
                      <text class="text-xs overflow-hidden text-ellipsis max-w-[220px] md:max-w-[240px] lg:max-w-[400px]">
                        <text>Add details</text>
                      </text>
                    </row>
                    <button class="text-blue-500 hover:text-blue-700 text-xs font-medium flex items-center gap-1 opacity-70 cursor-not-allowed" type="submit">
                      <box class="lucide lucide-lock" />
                      <text>Add</text>
                    </button>
                  </row>
                </box>
                <box class="p-2 border rounded-lg font-mono cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.5),inset_0_-1px_0_rgba(0,0,0,0.02)] dark:shadow-[inset_0_-1px_0_rgba(0,0,0,0.2)] bg-neutral-100 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-200/50 dark:hover:bg-neutral-700/50">
                  <row class="flex justify-between items-center">
                    <row class="flex items-center">
                      <row class="w-3 h-3 mr-2 rounded-full border flex items-center justify-center border-neutral-200 bg-neutral-100" />
                      <text class="text-xs overflow-hidden text-ellipsis max-w-[220px] md:max-w-[240px] lg:max-w-[400px]">
                        <text>Change texts, names, brands</text>
                      </text>
                    </row>
                    <button class="text-blue-500 hover:text-blue-700 text-xs font-medium flex items-center gap-1 opacity-70 cursor-not-allowed" type="submit">
                      <box class="lucide lucide-lock" />
                      <text>Add</text>
                    </button>
                  </row>
                </box>
                <box class="p-2 border rounded-lg font-mono cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.5),inset_0_-1px_0_rgba(0,0,0,0.02)] dark:shadow-[inset_0_-1px_0_rgba(0,0,0,0.2)] bg-neutral-100 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-200/50 dark:hover:bg-neutral-700/50">
                  <row class="flex justify-between items-center">
                    <row class="flex items-center">
                      <row class="w-3 h-3 mr-2 rounded-full border flex items-center justify-center border-neutral-200 bg-neutral-100" />
                      <text class="text-xs overflow-hidden text-ellipsis max-w-[220px] md:max-w-[240px] lg:max-w-[400px]">
                        <text>Use Lucide icons</text>
                      </text>
                    </row>
                    <button class="text-blue-500 hover:text-blue-700 text-xs font-medium flex items-center gap-1 opacity-70 cursor-not-allowed" type="submit">
                      <box class="lucide lucide-lock" />
                      <text>Add</text>
                    </button>
                  </row>
                </box>
                <box class="p-2 border rounded-lg font-mono cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.5),inset_0_-1px_0_rgba(0,0,0,0.02)] dark:shadow-[inset_0_-1px_0_rgba(0,0,0,0.2)] bg-neutral-100 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-200/50 dark:hover:bg-neutral-700/50">
                  <row class="flex justify-between items-center">
                    <row class="flex items-center">
                      <row class="w-3 h-3 mr-2 rounded-full border flex items-center justify-center border-neutral-200 bg-neutral-100" />
                      <text class="text-xs overflow-hidden text-ellipsis max-w-[220px] md:max-w-[240px] lg:max-w-[400px]">
                        <text>Change colors</text>
                      </text>
                    </row>
                    <button class="text-blue-500 hover:text-blue-700 text-xs font-medium flex items-center gap-1 opacity-70 cursor-not-allowed" type="submit">
                      <box class="lucide lucide-lock" />
                      <text>Add</text>
                    </button>
                  </row>
                </box>
                <box class="p-2 border rounded-lg font-mono cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.5),inset_0_-1px_0_rgba(0,0,0,0.02)] dark:shadow-[inset_0_-1px_0_rgba(0,0,0,0.2)] bg-neutral-100 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-200/50 dark:hover:bg-neutral-700/50">
                  <row class="flex justify-between items-center">
                    <row class="flex items-center">
                      <row class="w-3 h-3 mr-2 rounded-full border flex items-center justify-center border-neutral-200 bg-neutral-100" />
                      <text class="text-xs overflow-hidden text-ellipsis max-w-[220px] md:max-w-[240px] lg:max-w-[400px]">
                        <text>Make responsive</text>
                      </text>
                    </row>
                    <button class="text-blue-500 hover:text-blue-700 text-xs font-medium flex items-center gap-1 opacity-70 cursor-not-allowed" type="submit">
                      <box class="lucide lucide-lock" />
                      <text>Add</text>
                    </button>
                  </row>
                </box>
                <box class="p-2 border rounded-lg font-mono cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.5),inset_0_-1px_0_rgba(0,0,0,0.02)] dark:shadow-[inset_0_-1px_0_rgba(0,0,0,0.2)] bg-neutral-100 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-200/50 dark:hover:bg-neutral-700/50">
                  <row class="flex justify-between items-center">
                    <row class="flex items-center">
                      <row class="w-3 h-3 mr-2 rounded-full border flex items-center justify-center border-neutral-200 bg-neutral-100" />
                      <text class="text-xs overflow-hidden text-ellipsis max-w-[220px] md:max-w-[240px] lg:max-w-[400px]">
                        <text>In the style of Apple</text>
                      </text>
                    </row>
                    <button class="text-blue-500 hover:text-blue-700 text-xs font-medium flex items-center gap-1 opacity-70 cursor-not-allowed" type="submit">
                      <box class="lucide lucide-lock" />
                      <text>Add</text>
                    </button>
                  </row>
                </box>
                <box class="p-2 border rounded-lg font-mono cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.5),inset_0_-1px_0_rgba(0,0,0,0.02)] dark:shadow-[inset_0_-1px_0_rgba(0,0,0,0.2)] bg-neutral-100 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-200/50 dark:hover:bg-neutral-700/50">
                  <row class="flex justify-between items-center">
                    <row class="flex items-center">
                      <row class="w-3 h-3 mr-2 rounded-full border flex items-center justify-center border-neutral-200 bg-neutral-100" />
                      <text class="text-xs overflow-hidden text-ellipsis max-w-[220px] md:max-w-[240px] lg:max-w-[400px]">
                        <text>Adapt to dark mode</text>
                      </text>
                    </row>
                    <button class="text-blue-500 hover:text-blue-700 text-xs font-medium flex items-center gap-1 opacity-70 cursor-not-allowed" type="submit">
                      <box class="lucide lucide-lock" />
                      <text>Add</text>
                    </button>
                  </row>
                </box>
                <box class="p-2 border rounded-lg font-mono cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.5),inset_0_-1px_0_rgba(0,0,0,0.02)] dark:shadow-[inset_0_-1px_0_rgba(0,0,0,0.2)] bg-neutral-100 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-200/50 dark:hover:bg-neutral-700/50">
                  <row class="flex justify-between items-center">
                    <row class="flex items-center">
                      <row class="w-3 h-3 mr-2 rounded-full border flex items-center justify-center border-neutral-200 bg-neutral-100" />
                      <text class="text-xs overflow-hidden text-ellipsis max-w-[220px] md:max-w-[240px] lg:max-w-[400px]">
                        <text>Add hover states</text>
                      </text>
                    </row>
                    <button class="text-blue-500 hover:text-blue-700 text-xs font-medium flex items-center gap-1 opacity-70 cursor-not-allowed" type="submit">
                      <box class="lucide lucide-lock" />
                      <text>Add</text>
                    </button>
                  </row>
                </box>
                <box class="p-2 border rounded-lg font-mono cursor-pointer shadow-[inset_0_1px_0_rgba(255,255,255,0.5),inset_0_-1px_0_rgba(0,0,0,0.02)] dark:shadow-[inset_0_-1px_0_rgba(0,0,0,0.2)] bg-neutral-100 dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-200/50 dark:hover:bg-neutral-700/50">
                  <row class="flex justify-between items-center">
                    <row class="flex items-center">
                      <row class="w-3 h-3 mr-2 rounded-full border flex items-center justify-center border-neutral-200 bg-neutral-100" />
                      <text class="text-xs overflow-hidden text-ellipsis max-w-[220px] md:max-w-[240px] lg:max-w-[400px]">
                        <text>Animate sequence fade in, slide in, blur in</text>
                      </text>
                    </row>
                    <button class="text-blue-500 hover:text-blue-700 text-xs font-medium flex items-center gap-1 opacity-70 cursor-not-allowed" type="submit">
                      <box class="lucide lucide-lock" />
                      <text>Add</text>
                    </button>
                  </row>
                </box>
              </box>
            </box>
          </column>
        </row>
      </column>
    </component>
  </components>

  <screens>
    <screen name="GeneratedScreen">
      <column class="relative bg-white dark:bg-neutral-900 border border-transparent dark:border-neutral-800 rounded-2xl shadow-xl w-11/12 max-w-6xl h-[85vh] flex flex-col" />
    </screen>
  </screens>
</udml>