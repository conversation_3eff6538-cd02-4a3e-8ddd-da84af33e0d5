<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{BUSINESS_NAME}} - Online Store</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #ffffff;
        }
        
        .product-card {
            background: #ffffff;
            border-radius: 8px;
            transition: all 0.2s ease;
            border: 1px solid #f3f4f6;
        }
        
        .product-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .filter-button {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 24px;
            padding: 8px 16px;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .filter-button:hover {
            border-color: #d1d5db;
        }
        
        .filter-button.active {
            background: #000000;
            color: #ffffff;
            border-color: #000000;
        }
        
        .color-dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid #ffffff;
            box-shadow: 0 0 0 1px #e5e7eb;
        }
        
        .nav-link {
            color: #374151;
            text-decoration: none;
            padding: 8px 0;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }
        
        .nav-link:hover {
            color: #000000;
        }
        
        .nav-link.active {
            color: #000000;
            border-bottom-color: #000000;
        }
    </style>
</head>
<body class="bg-white">
    
    <!-- Header -->
    <header class="border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-black">{{BUSINESS_NAME}}</h1>
                </div>
                
                <!-- Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="#" class="nav-link active">Shop</a>
                    <a href="#" class="nav-link">Collections</a>
                    <a href="#" class="nav-link">Explore</a>
                    <a href="#" class="nav-link">About</a>
                </nav>
                
                <!-- Cart & Account -->
                <div class="flex items-center space-x-4">
                    <button class="text-gray-700 hover:text-black">
                        🛒 Cart 0
                    </button>
                    <button class="text-gray-700 hover:text-black">
                        👤 My account
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- Hero Section -->
        <section class="mb-12">
            <h2 class="text-4xl font-bold text-black mb-4">Get Inspired</h2>
            <p class="text-gray-600 text-lg max-w-2xl">
                {{HERO_SUBTITLE}}
            </p>
        </section>

        <!-- Filters -->
        <section class="mb-8">
            <div class="flex flex-wrap gap-4">
                <button class="filter-button active">
                    <span class="text-sm font-medium">Category</span><br>
                    <span class="text-xs text-gray-500">All Categories</span>
                </button>
                <button class="filter-button">
                    <span class="text-sm font-medium">Color</span><br>
                    <span class="text-xs text-gray-500">All Colors</span>
                </button>
                <button class="filter-button">
                    <span class="text-sm font-medium">Features</span><br>
                    <span class="text-xs text-gray-500">All Features</span>
                </button>
                <button class="filter-button">
                    <span class="text-sm font-medium">Price</span><br>
                    <span class="text-xs text-gray-500">From €0 - €1000</span>
                </button>
                <button class="filter-button">
                    <span class="text-sm font-medium">Sort</span><br>
                    <span class="text-xs text-gray-500">New In</span>
                </button>
            </div>
        </section>

        <!-- Products Grid -->
        <section class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            
            <!-- Product 1 -->
            <div class="product-card p-4">
                <div class="relative mb-4">
                    <div class="aspect-square bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-lg flex items-center justify-center mb-2">
                        <span class="text-6xl">{{PRODUCT_1_EMOJI}}</span>
                    </div>
                    <div class="absolute top-2 right-2 flex space-x-1">
                        <div class="color-dot bg-yellow-400"></div>
                        <span class="text-xs text-gray-500">+1</span>
                    </div>
                </div>
                <h3 class="font-medium text-black mb-1">{{PRODUCT_1_NAME}}</h3>
                <p class="text-sm text-gray-600 mb-2">{{PRODUCT_1_DESCRIPTION}}</p>
                <p class="font-semibold text-black">€{{PRODUCT_1_PRICE}}</p>
            </div>

            <!-- Product 2 -->
            <div class="product-card p-4">
                <div class="relative mb-4">
                    <div class="aspect-square bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center mb-2">
                        <span class="text-6xl">{{PRODUCT_2_EMOJI}}</span>
                    </div>
                    <div class="absolute top-2 right-2 flex space-x-1">
                        <div class="color-dot bg-gray-400"></div>
                        <span class="text-xs text-gray-500">+1</span>
                    </div>
                </div>
                <h3 class="font-medium text-black mb-1">{{PRODUCT_2_NAME}}</h3>
                <p class="text-sm text-gray-600 mb-2">{{PRODUCT_2_DESCRIPTION}}</p>
                <p class="font-semibold text-black">€{{PRODUCT_2_PRICE}}</p>
            </div>

            <!-- Product 3 -->
            <div class="product-card p-4">
                <div class="relative mb-4">
                    <div class="aspect-square bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center mb-2">
                        <span class="text-6xl">{{PRODUCT_3_EMOJI}}</span>
                    </div>
                    <div class="absolute top-2 right-2 flex space-x-1">
                        <div class="color-dot bg-green-600"></div>
                        <span class="text-xs text-gray-500">+2</span>
                    </div>
                </div>
                <h3 class="font-medium text-black mb-1">{{PRODUCT_3_NAME}}</h3>
                <p class="text-sm text-gray-600 mb-2">{{PRODUCT_3_DESCRIPTION}}</p>
                <p class="font-semibold text-black">€{{PRODUCT_3_PRICE}}</p>
            </div>

            <!-- Product 4 -->
            <div class="product-card p-4">
                <div class="relative mb-4">
                    <div class="aspect-square bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center mb-2">
                        <span class="text-6xl">{{PRODUCT_4_EMOJI}}</span>
                    </div>
                    <div class="absolute top-2 right-2 flex space-x-1">
                        <div class="color-dot bg-blue-600"></div>
                        <span class="text-xs text-gray-500">+2</span>
                    </div>
                </div>
                <h3 class="font-medium text-black mb-1">{{PRODUCT_4_NAME}}</h3>
                <p class="text-sm text-gray-600 mb-2">{{PRODUCT_4_DESCRIPTION}}</p>
                <p class="font-semibold text-black">€{{PRODUCT_4_PRICE}}</p>
            </div>

            <!-- Product 5 -->
            <div class="product-card p-4">
                <div class="relative mb-4">
                    <div class="aspect-square bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg flex items-center justify-center mb-2">
                        <span class="text-6xl">{{PRODUCT_5_EMOJI}}</span>
                    </div>
                    <div class="absolute top-2 right-2 flex space-x-1">
                        <div class="color-dot bg-purple-600"></div>
                        <span class="text-xs text-gray-500">+1</span>
                    </div>
                </div>
                <h3 class="font-medium text-black mb-1">{{PRODUCT_5_NAME}}</h3>
                <p class="text-sm text-gray-600 mb-2">{{PRODUCT_5_DESCRIPTION}}</p>
                <p class="font-semibold text-black">€{{PRODUCT_5_PRICE}}</p>
            </div>

            <!-- Product 6 -->
            <div class="product-card p-4">
                <div class="relative mb-4">
                    <div class="aspect-square bg-gradient-to-br from-red-100 to-red-200 rounded-lg flex items-center justify-center mb-2">
                        <span class="text-6xl">{{PRODUCT_6_EMOJI}}</span>
                    </div>
                    <div class="absolute top-2 right-2 flex space-x-1">
                        <div class="color-dot bg-red-600"></div>
                        <span class="text-xs text-gray-500">+3</span>
                    </div>
                </div>
                <h3 class="font-medium text-black mb-1">{{PRODUCT_6_NAME}}</h3>
                <p class="text-sm text-gray-600 mb-2">{{PRODUCT_6_DESCRIPTION}}</p>
                <p class="font-semibold text-black">€{{PRODUCT_6_PRICE}}</p>
            </div>

            <!-- Product 7 -->
            <div class="product-card p-4">
                <div class="relative mb-4">
                    <div class="aspect-square bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-lg flex items-center justify-center mb-2">
                        <span class="text-6xl">{{PRODUCT_7_EMOJI}}</span>
                    </div>
                    <div class="absolute top-2 right-2 flex space-x-1">
                        <div class="color-dot bg-indigo-600"></div>
                        <span class="text-xs text-gray-500">+2</span>
                    </div>
                </div>
                <h3 class="font-medium text-black mb-1">{{PRODUCT_7_NAME}}</h3>
                <p class="text-sm text-gray-600 mb-2">{{PRODUCT_7_DESCRIPTION}}</p>
                <p class="font-semibold text-black">€{{PRODUCT_7_PRICE}}</p>
            </div>

            <!-- Product 8 -->
            <div class="product-card p-4">
                <div class="relative mb-4">
                    <div class="aspect-square bg-gradient-to-br from-pink-100 to-pink-200 rounded-lg flex items-center justify-center mb-2">
                        <span class="text-6xl">{{PRODUCT_8_EMOJI}}</span>
                    </div>
                    <div class="absolute top-2 right-2 flex space-x-1">
                        <div class="color-dot bg-pink-600"></div>
                        <span class="text-xs text-gray-500">+1</span>
                    </div>
                </div>
                <h3 class="font-medium text-black mb-1">{{PRODUCT_8_NAME}}</h3>
                <p class="text-sm text-gray-600 mb-2">{{PRODUCT_8_DESCRIPTION}}</p>
                <p class="font-semibold text-black">€{{PRODUCT_8_PRICE}}</p>
            </div>
        </section>

        <!-- Load More -->
        <section class="text-center mt-12">
            <button class="bg-black text-white px-8 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors">
                Load More Products
            </button>
        </section>

        <!-- About Section -->
        <section class="mt-16 bg-gray-50 rounded-lg p-8">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-2xl font-bold text-black mb-4">About {{BUSINESS_NAME}}</h2>
                <p class="text-gray-600 mb-6">{{ABOUT_TEXT}}</p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div>
                        <div class="text-2xl font-bold text-black">{{YEARS_EXPERIENCE}}+</div>
                        <div class="text-sm text-gray-600">Years Experience</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-black">{{HAPPY_CUSTOMERS}}+</div>
                        <div class="text-sm text-gray-600">Happy Customers</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-black">{{PRODUCTS_COUNT}}+</div>
                        <div class="text-sm text-gray-600">Products</div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="font-bold mb-4">{{BUSINESS_NAME}}</h3>
                    <p class="text-gray-400 text-sm">{{BUSINESS_TAGLINE}}</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Shop</h4>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="#" class="hover:text-white">All Products</a></li>
                        <li><a href="#" class="hover:text-white">New Arrivals</a></li>
                        <li><a href="#" class="hover:text-white">Best Sellers</a></li>
                        <li><a href="#" class="hover:text-white">Sale</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="#" class="hover:text-white">Contact Us</a></li>
                        <li><a href="#" class="hover:text-white">Shipping Info</a></li>
                        <li><a href="#" class="hover:text-white">Returns</a></li>
                        <li><a href="#" class="hover:text-white">Size Guide</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Connect</h4>
                    <div class="text-sm text-gray-400">
                        <p>{{BUSINESS_PHONE}}</p>
                        <p>{{BUSINESS_ADDRESS}}</p>
                        <p class="mt-2">{{BUSINESS_HOURS}}</p>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-sm text-gray-400">
                <p>&copy; 2024 {{BUSINESS_NAME}}. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Add interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Filter buttons
            const filterButtons = document.querySelectorAll('.filter-button');
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                });
            });
            
            // Product cards hover effect
            const productCards = document.querySelectorAll('.product-card');
            productCards.forEach(card => {
                card.addEventListener('click', function() {
                    console.log('Product clicked:', this.querySelector('h3').textContent);
                });
            });
            
            // Navigation links
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    navLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>

</body>
</html>