<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{BUSINESS_NAME}} - Facebook Shop</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
        }
        
        .neomorphism {
            background: #f1f5f9;
            border-radius: 16px;
            box-shadow: 
                8px 8px 16px rgba(148, 163, 184, 0.3),
                -8px -8px 16px rgba(255, 255, 255, 0.8);
        }
        
        .neomorphism-inset {
            background: #f1f5f9;
            border-radius: 12px;
            box-shadow: 
                inset 4px 4px 8px rgba(148, 163, 184, 0.3),
                inset -4px -4px 8px rgba(255, 255, 255, 0.8);
        }
        
        .product-card {
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 
                4px 4px 12px rgba(148, 163, 184, 0.2),
                -4px -4px 12px rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }
        
        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: 
                8px 8px 20px rgba(148, 163, 184, 0.3),
                -8px -8px 20px rgba(255, 255, 255, 0.9);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #1877f2, #42a5f5);
            border-radius: 12px;
            box-shadow: 
                4px 4px 8px rgba(24, 119, 242, 0.3),
                -2px -2px 8px rgba(255, 255, 255, 0.8);
        }
        
        .btn-primary:hover {
            box-shadow: 
                2px 2px 4px rgba(24, 119, 242, 0.3),
                -1px -1px 4px rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    
    <!-- Header Section -->
    <div class="neomorphism mx-8 mt-8 p-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6">
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center">
                    <span class="text-2xl">{{BUSINESS_EMOJI}}</span>
                </div>
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">{{BUSINESS_NAME}}</h1>
                    <p class="text-gray-600">{{BUSINESS_TAGLINE}}</p>
                </div>
            </div>
            <div class="flex space-x-4">
                <button class="btn-primary text-white px-6 py-3 font-semibold">
                    💬 Message Us
                </button>
                <button class="neomorphism px-6 py-3 text-gray-700 font-semibold">
                    ⭐ Follow
                </button>
            </div>
        </div>
    </div>

    <!-- Hero Banner -->
    <div class="neomorphism mx-8 mt-8 p-8">
        <div class="text-center">
            <h2 class="text-4xl font-bold text-gray-800 mb-4">{{HERO_TITLE}}</h2>
            <p class="text-xl text-gray-600 mb-8">{{HERO_SUBTITLE}}</p>
            <div class="w-full h-64 neomorphism-inset rounded-lg bg-gradient-to-r from-blue-100 to-purple-100 flex items-center justify-center">
                <span class="text-6xl">{{HERO_IMAGE_PLACEHOLDER}}</span>
            </div>
        </div>
    </div>

    <!-- Categories -->
    <div class="mx-8 mt-8">
        <h3 class="text-2xl font-bold text-gray-800 mb-6">Shop by Category</h3>
        <div class="grid grid-cols-4 gap-6">
            <div class="neomorphism p-6 text-center cursor-pointer hover:transform hover:scale-105 transition-transform">
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">👕</span>
                </div>
                <span class="font-semibold text-gray-700">Clothing</span>
            </div>
            <div class="neomorphism p-6 text-center cursor-pointer hover:transform hover:scale-105 transition-transform">
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">👠</span>
                </div>
                <span class="font-semibold text-gray-700">Shoes</span>
            </div>
            <div class="neomorphism p-6 text-center cursor-pointer hover:transform hover:scale-105 transition-transform">
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">👜</span>
                </div>
                <span class="font-semibold text-gray-700">Accessories</span>
            </div>
            <div class="neomorphism p-6 text-center cursor-pointer hover:transform hover:scale-105 transition-transform">
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">🎁</span>
                </div>
                <span class="font-semibold text-gray-700">Gifts</span>
            </div>
        </div>
    </div>

    <!-- Featured Products -->
    <div class="mx-8 mt-8">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-2xl font-bold text-gray-800">Featured Products</h3>
            <button class="text-blue-600 font-semibold hover:underline">View All</button>
        </div>
        <div class="grid grid-cols-3 gap-6">
            <!-- Product 1 -->
            <div class="product-card p-6">
                <div class="w-full h-48 bg-gradient-to-br from-pink-100 to-pink-200 rounded-lg mb-4 flex items-center justify-center">
                    <span class="text-4xl">👕</span>
                </div>
                <h4 class="font-bold text-gray-800 mb-2">{{PRODUCT_1_NAME}}</h4>
                <p class="text-gray-600 text-sm mb-4">{{PRODUCT_1_DESCRIPTION}}</p>
                <div class="flex justify-between items-center">
                    <span class="text-2xl font-bold text-blue-600">${{PRODUCT_1_PRICE}}</span>
                    <button class="btn-primary text-white px-4 py-2 text-sm font-semibold">
                        Add to Cart
                    </button>
                </div>
            </div>
            
            <!-- Product 2 -->
            <div class="product-card p-6">
                <div class="w-full h-48 bg-gradient-to-br from-green-100 to-green-200 rounded-lg mb-4 flex items-center justify-center">
                    <span class="text-4xl">👠</span>
                </div>
                <h4 class="font-bold text-gray-800 mb-2">{{PRODUCT_2_NAME}}</h4>
                <p class="text-gray-600 text-sm mb-4">{{PRODUCT_2_DESCRIPTION}}</p>
                <div class="flex justify-between items-center">
                    <span class="text-2xl font-bold text-blue-600">${{PRODUCT_2_PRICE}}</span>
                    <button class="btn-primary text-white px-4 py-2 text-sm font-semibold">
                        Add to Cart
                    </button>
                </div>
            </div>
            
            <!-- Product 3 -->
            <div class="product-card p-6">
                <div class="w-full h-48 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg mb-4 flex items-center justify-center">
                    <span class="text-4xl">👜</span>
                </div>
                <h4 class="font-bold text-gray-800 mb-2">{{PRODUCT_3_NAME}}</h4>
                <p class="text-gray-600 text-sm mb-4">{{PRODUCT_3_DESCRIPTION}}</p>
                <div class="flex justify-between items-center">
                    <span class="text-2xl font-bold text-blue-600">${{PRODUCT_3_PRICE}}</span>
                    <button class="btn-primary text-white px-4 py-2 text-sm font-semibold">
                        Add to Cart
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- About Section -->
    <div class="neomorphism mx-8 mt-8 p-8">
        <div class="grid grid-cols-2 gap-8 items-center">
            <div>
                <h3 class="text-2xl font-bold text-gray-800 mb-4">About {{BUSINESS_NAME}}</h3>
                <p class="text-gray-600 mb-6">{{ABOUT_TEXT}}</p>
                <div class="flex space-x-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{YEARS_EXPERIENCE}}+</div>
                        <div class="text-sm text-gray-600">Years Experience</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{HAPPY_CUSTOMERS}}+</div>
                        <div class="text-sm text-gray-600">Happy Customers</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{PRODUCTS_COUNT}}+</div>
                        <div class="text-sm text-gray-600">Products</div>
                    </div>
                </div>
            </div>
            <div class="neomorphism-inset rounded-lg h-64 flex items-center justify-center">
                <span class="text-6xl">{{ABOUT_IMAGE_PLACEHOLDER}}</span>
            </div>
        </div>
    </div>

    <!-- Contact & Location -->
    <div class="neomorphism mx-8 mt-8 mb-8 p-8">
        <div class="grid grid-cols-3 gap-8 text-center">
            <div>
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">📍</span>
                </div>
                <h4 class="font-bold text-gray-800 mb-2">Visit Our Store</h4>
                <p class="text-gray-600">{{BUSINESS_ADDRESS}}</p>
            </div>
            <div>
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">📞</span>
                </div>
                <h4 class="font-bold text-gray-800 mb-2">Call Us</h4>
                <p class="text-gray-600">{{BUSINESS_PHONE}}</p>
            </div>
            <div>
                <div class="w-16 h-16 neomorphism-inset rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">⏰</span>
                </div>
                <h4 class="font-bold text-gray-800 mb-2">Store Hours</h4>
                <p class="text-gray-600">{{BUSINESS_HOURS}}</p>
            </div>
        </div>
    </div>

    <script>
        // Add interactive functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add to cart functionality
            const addToCartButtons = document.querySelectorAll('.btn-primary');
            
            addToCartButtons.forEach(button => {
                if (button.textContent.includes('Add to Cart')) {
                    button.addEventListener('click', function() {
                        this.textContent = '✅ Added!';
                        this.style.background = 'linear-gradient(135deg, #10b981, #34d399)';
                        
                        setTimeout(() => {
                            this.textContent = 'Add to Cart';
                            this.style.background = 'linear-gradient(135deg, #1877f2, #42a5f5)';
                        }, 2000);
                    });
                }
            });
            
            // Category hover effects
            const categoryCards = document.querySelectorAll('.neomorphism');
            categoryCards.forEach(card => {
                if (card.classList.contains('cursor-pointer')) {
                    card.addEventListener('click', function() {
                        console.log('Category clicked:', this.textContent.trim());
                    });
                }
            });
        });
    </script>

</body>
</html>